sources:
  sample-database:
    kind: "sqlite"
    database: "./sample_database.db"

tools:
  # User management tools
  read_all_users:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT * FROM users ORDER BY created_at DESC"

  read_user_by_id:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT * FROM users WHERE id = ?"

  read_users_by_age_range:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT * FROM users WHERE age BETWEEN ? AND ? ORDER BY age"

  create_user:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "INSERT INTO users (name, email, age) VALUES (?, ?, ?)"

  # Product management tools
  read_all_products:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT * FROM products ORDER BY created_at DESC"

  read_products_by_category:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT * FROM products WHERE category = ? ORDER BY name"

  read_products_in_stock:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT * FROM products WHERE in_stock = 1 ORDER BY price"

  read_products_by_price_range:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT * FROM products WHERE price BETWEEN ? AND ? ORDER BY price"

  create_product:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "INSERT INTO products (name, price, category, in_stock) VALUES (?, ?, ?, ?)"

  update_product_stock:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "UPDATE products SET in_stock = ? WHERE id = ?"

  # Analytics tools
  count_users:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT COUNT(*) as total_users FROM users"

  count_products:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT COUNT(*) as total_products FROM products"

  products_by_category_count:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT category, COUNT(*) as count FROM products GROUP BY category ORDER BY count DESC"

  average_product_price:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT category, AVG(price) as avg_price FROM products GROUP BY category ORDER BY avg_price DESC"

  users_age_stats:
    kind: "sqlite-sql"
    source: "sample-database"
    statement: "SELECT MIN(age) as min_age, MAX(age) as max_age, AVG(age) as avg_age FROM users"