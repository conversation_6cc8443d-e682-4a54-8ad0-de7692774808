import sqlite3
from datetime import datetime

def create_database():
    """Create SQLite database with two tables and sample data"""

    # Connect to SQLite database (creates file if it doesn't exist)
    conn = sqlite3.connect('sample_database.db')
    cursor = conn.cursor()

    # Create first table: Users
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            age INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create second table: Products
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            price REAL NOT NULL,
            category TEXT,
            in_stock BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Insert sample data into users table
    users_data = [
        ('<PERSON>', '<EMAIL>', 28),
        ('<PERSON>', '<EMAIL>', 34),
        ('<PERSON>', '<EMAIL>', 22),
        ('<PERSON>', '<EMAIL>', 29),
        ('Charlie Wilson', '<EMAIL>', 31)
    ]

    cursor.executemany('''
        INSERT OR IGNORE INTO users (name, email, age)
        VALUES (?, ?, ?)
    ''', users_data)

    # Insert sample data into products table
    products_data = [
        ('Laptop', 999.99, 'Electronics', True),
        ('Smartphone', 699.99, 'Electronics', True),
        ('Coffee Mug', 12.99, 'Kitchen', True),
        ('Desk Chair', 199.99, 'Furniture', False),
        ('Notebook', 5.99, 'Stationery', True),
        ('Headphones', 149.99, 'Electronics', True),
        ('Water Bottle', 24.99, 'Sports', True)
    ]

    cursor.executemany('''
        INSERT OR IGNORE INTO products (name, price, category, in_stock)
        VALUES (?, ?, ?, ?)
    ''', products_data)

    # Commit changes and close connection
    conn.commit()

    print("Database created successfully!")
    print(f"Users table: {cursor.execute('SELECT COUNT(*) FROM users').fetchone()[0]} records")
    print(f"Products table: {cursor.execute('SELECT COUNT(*) FROM products').fetchone()[0]} records")

    return conn

def display_data(conn):
    """Display data from both tables"""
    cursor = conn.cursor()

    print("\n=== USERS TABLE ===")
    cursor.execute('SELECT * FROM users')
    users = cursor.fetchall()
    print("ID | Name | Email | Age | Created At")
    print("-" * 60)
    for user in users:
        print(f"{user[0]} | {user[1]} | {user[2]} | {user[3]} | {user[4]}")

    print("\n=== PRODUCTS TABLE ===")
    cursor.execute('SELECT * FROM products')
    products = cursor.fetchall()
    print("ID | Name | Price | Category | In Stock | Created At")
    print("-" * 70)
    for product in products:
        print(f"{product[0]} | {product[1]} | ${product[2]} | {product[3]} | {product[4]} | {product[5]}")

def main():
    """Main function to create database and display data"""
    try:
        # Create database and tables with data
        conn = create_database()

        # Display the data
        display_data(conn)

        # Close connection
        conn.close()

        print("\nDatabase operations completed successfully!")
        print("Database file 'sample_database.db' has been created in the current directory.")

    except sqlite3.Error as e:
        print(f"An error occurred: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    main()