import asyncio
import logging
from toolbox_core import ToolboxClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# update the url to point to your server
client = ToolboxClient("http://127.0.0.1:5000")

async def main():
    """Main function to load toolset and log results"""
    try:
        logger.info("Starting MCP Toolbox client...")
        logger.info("Connecting to server at: http://127.0.0.1:5000")

        # Load the toolset
        logger.info("Loading toolset: 'toolset_name'")
        tools = await client.load_toolset("toolset_name")

        # Log the tools that were returned
        if tools:
            logger.info(f"Successfully loaded {len(tools)} tools:")
            for i, tool in enumerate(tools, 1):
                tool_name = getattr(tool, 'name', 'Unknown')
                tool_description = getattr(tool, 'description', 'No description')
                logger.info(f"  {i}. {tool_name}: {tool_description}")
        else:
            logger.warning("No tools were returned from the toolset")

        logger.info("✅ Process completed successfully!")
        return tools

    except Exception as e:
        logger.error(f"❌ Process failed with error: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        raise

if __name__ == "__main__":
    # these tools can be passed to your application!
    try:
        tools = asyncio.run(main())
        print(f"\nTools loaded and ready to use: {len(tools) if tools else 0} tools available")
    except Exception as e:
        print(f"\nFailed to load tools: {e}")
        exit(1)
